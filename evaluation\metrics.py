# evaluation/metrics.py

import evaluate
import numpy as np
import json
import requests
import time
from tqdm import tqdm
import concurrent.futures



# -------------------------------------------------------------------
# 1. 各个指标的独立计算函数
# -------------------------------------------------------------------

def calculate_bert_score(predictions, references):
    """计算BERTScore (P, R, F1) 的均值和标准差"""
    print("Calculating BERTScore...")
    try:
        bertscore = evaluate.load("bertscore")
        results = bertscore.compute(
            predictions=predictions,
            references=references,
            lang="zh",  # 假设您的课程数据是英文，如果是中文，可以改为 "zh"
            model_type="bert-base-multilingual-cased",  # 推荐使用多语言模型以获得更好的泛化性
            rescale_with_baseline=True,
        )
        precision = results["precision"]
        recall = results["recall"]
        f1 = results["f1"]
        return {
            "bert_precision_mean": np.mean(precision), "bert_precision_std": np.std(precision),
            "bert_recall_mean": np.mean(recall), "bert_recall_std": np.std(recall),
            "bert_f1_mean": np.mean(f1), "bert_f1_std": np.std(f1),
        }
    except Exception as e:
        print(f"Error calculating BERTScore: {e}")
        return {}


def calculate_bleurt_score(predictions, references):
    """计算BLEURT的均值和标准差"""
    print("Calculating BLEURT Score...")
    try:
        bleurt = evaluate.load("bleurt", module_type="metric", checkpoint="BLEURT-20")
        results = bleurt.compute(predictions=predictions, references=references)
        scores = results["scores"]
        return {"bleurt_mean": np.mean(scores), "bleurt_std": np.std(scores)}
    except Exception as e:
        print(f"Error calculating BLEURT Score: {e}")
        return {}


def calculate_bart_score(predictions, references):
    """计算BARTScore的均值和标准差"""
    print("Calculating BARTScore...")
    try:
        bart_scorer = evaluate.load("bartscore")
        # 计算P, R, F1
        results_p = bart_scorer.compute(predictions=predictions, references=references, segment_scores=True)
        results_r = bart_scorer.compute(predictions=references, references=predictions, segment_scores=True)

        precision = np.array(results_p["score"])
        recall = np.array(results_r["score"])
        f1 = 2 * (precision * recall) / (precision + recall)

        return {
            "bart_f1_mean": np.mean(f1), "bart_f1_std": np.std(f1)
        }
    except Exception as e:
        print(f"Error calculating BARTScore: {e}")
        return {}


def calculate_usr(predictions):
    """计算USR (唯一句子率)"""
    print("Calculating USR...")
    tokens_predict = [s.split() for s in predictions]
    unique_seq = []
    for seq in tokens_predict:
        is_duplicate = any(seq == uni_seq for uni_seq in unique_seq)
        if not is_duplicate:
            unique_seq.append(seq)
    usr = len(unique_seq) / len(predictions) if predictions else 0
    return {"usr": usr}


# -------------------------------------------------------------------
# 2. GPTScore的实现 (适配硅基流动API)
# -------------------------------------------------------------------

class GPTScorer:
    def __init__(self, api_key, system_prompt):
        self.api_key = api_key
        self.system_prompt = system_prompt
        self.api_url = "https://api.siliconflow.cn/v1/chat/completions"
        self.model_name = "Qwen/Qwen3-8B"  # 或者您选择的其他模型
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

    def _get_single_score(self, prompt_data):
        """为单个样本调用API并获取分数，包含重试逻辑"""
        prediction, reference = prompt_data
        prompt = json.dumps({"prediction": prediction, "reference": reference})

        max_retries = 3
        base_delay = 2
        for attempt in range(max_retries):
            try:
                payload = {
                    "model": self.model_name,
                    "messages": [
                        {"role": "system", "content": self.system_prompt},
                        {"role": "user", "content": prompt}
                    ],
                    "temperature": 0.0,  # 评估时使用确定性输出
                    "max_tokens": 10
                }
                response = requests.post(self.api_url, json=payload, headers=self.headers, timeout=60)
                response.raise_for_status()
                response_data = response.json()
                # 获取模型返回的完整内容字符串
                content_str = response_data['choices'][0]['message']['content'].strip()

                # 尝试将字符串解析为JSON对象
                score_json = json.loads(content_str)

                # 从JSON对象中提取分数
                score_val = float(score_json['score'])
                return score_val
            except Exception as e:
                print(f"GPTScore API call failed (attempt {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(base_delay * (2 ** attempt))
                else:
                    return None  # 多次重试失败后返回None

    def calculate_gpt_score(self, predictions, references):
        """并行计算整个数据集的GPTScore"""
        print("Calculating GPTScore...")
        if not self.api_key:
            print("Warning: GPTScore requires an API key. Skipping.")
            return {}

        prompts = list(zip(predictions, references))
        results = []

        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            future_to_prompt = {executor.submit(self._get_single_score, p): p for p in prompts}

            for future in tqdm(concurrent.futures.as_completed(future_to_prompt), total=len(prompts),
                               desc="GPTScore Progress"):
                score = future.result()
                if score is not None:
                    results.append(score)

        if not results:
            return {}

        return {"gpt_score_mean": np.mean(results), "gpt_score_std": np.std(results)}