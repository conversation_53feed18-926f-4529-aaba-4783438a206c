# explainer/train.py

import os
import pickle
import json
import torch
from tqdm import tqdm
from explainer.models.ide_cot_r import Explainer
from explainer.utils.data_handler import DataHandler
from explainer.utils.parse import args

# 设置计算设备
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"Using device: {device}")


# 将主流程封装在一个类中
class Trainer:
    def __init__(self):
        print(f"Initializing trainer for dataset: {args.dataset}")
        self.model = Explainer().to(device)
        self.data_handler = DataHandler()
        self.trn_loader, self.val_loader, self.tst_loader = self.data_handler.get_data_loaders()
        self.model_save_dir = f"./checkpoints/{args.dataset}/"
        os.makedirs(self.model_save_dir, exist_ok=True)
        self.user_adapter_path = os.path.join(self.model_save_dir, "user_adapter.pt")
        self.item_adapter_path = os.path.join(self.model_save_dir, "item_adapter.pt")
        self.results_save_dir = f"./results/{args.dataset}/"
        os.makedirs(self.results_save_dir, exist_ok=True)
        self.predictions_path = os.path.join(self.results_save_dir, "predictions.pkl")
        self.references_path = os.path.join(self.results_save_dir, "references.pkl")
        self.predictions_json_path = os.path.join(self.results_save_dir, "predictions.jsonl")

    def train(self):
        optimizer = torch.optim.Adam(
            filter(lambda p: p.requires_grad, self.model.parameters()),
            lr=args.lr,
            weight_decay=args.weight_decay
        )
        print("Starting model finetuning...")
        for epoch in range(args.epochs):
            self.model.train()
            total_loss = 0
            progress_bar = tqdm(self.trn_loader, desc=f"Epoch {epoch + 1}/{args.epochs}")
            for i, batch in enumerate(progress_bar):
                user_vecs, item_vecs, prompts = batch
                user_vecs = user_vecs.to(device)
                item_vecs = item_vecs.to(device)
                optimizer.zero_grad()
                outputs, _ = self.model(user_vecs, item_vecs, prompts)
                loss = outputs.loss
                loss.backward()
                optimizer.step()
                total_loss += loss.item()
                progress_bar.set_postfix({"loss": loss.item()})
            avg_loss = total_loss / len(self.trn_loader)
            print(f"Epoch {epoch + 1} finished. Average Loss: {avg_loss:.4f}")
            torch.save(self.model.user_adapter.state_dict(), self.user_adapter_path)
            torch.save(self.model.item_adapter.state_dict(), self.item_adapter_path)
            print(f"Model adapters saved to {self.model_save_dir}")

    def generate(self):
        print("Starting explanation generation...")
        try:
            self.model.user_adapter.load_state_dict(torch.load(self.user_adapter_path))
            self.model.item_adapter.load_state_dict(torch.load(self.item_adapter_path))
            print("Successfully loaded trained model adapters.")
        except FileNotFoundError:
            print(f"Error: Model adapter checkpoints not found in {self.model_save_dir}. Please run training first.")
            return
        self.model.eval()
        predictions = []
        references = []
        json_results = []
        with torch.no_grad():
            progress_bar = tqdm(self.tst_loader, desc="Generating explanations on test set")
            for i, batch in enumerate(progress_bar):
                user_vecs, item_vecs, prompts, explains = batch
                user_vecs = user_vecs.to(device)
                item_vecs = item_vecs.to(device)
                generated_outputs = self.model.generate(user_vecs, item_vecs, prompts)

                # --- 核心修改: 新的后处理逻辑 ---
                # generated_outputs 是一个列表，包含了批次中每个样本的生成结果
                # prompts 也是一个列表，包含了对应的输入
                for idx, full_output in enumerate(generated_outputs):
                    input_prompt = prompts[idx]
                    # 检查生成文本是否以输入Prompt开头
                    if full_output.startswith(input_prompt):
                        # 如果是，则截取掉输入部分，只保留模型真正生成的内容
                        clean_text = full_output[len(input_prompt):].strip()
                    else:
                        # 如果不是（可能因为某些特殊token导致不完全匹配），
                        # 尝试从assistant标记后截取，作为备用方案
                        assistant_tag = "<|im_start|>assistant\n"
                        tag_pos = full_output.rfind(assistant_tag)
                        if tag_pos != -1:
                            clean_text = full_output[tag_pos + len(assistant_tag):].strip()
                        else:
                            clean_text = full_output  # 最坏情况，保留全部输出

                    # 去掉可能存在的结束标记
                    if clean_text.endswith(self.model.tokenizer.eos_token):
                        clean_text = clean_text[:-len(self.model.tokenizer.eos_token)].strip()

                    predictions.append(clean_text)
                    references.append(explains[idx])
                    json_results.append({
                        "prediction": clean_text,
                        "reference": explains[idx],
                        "original_prompt": prompts[idx]
                    })

        with open(self.predictions_path, "wb") as file:
            pickle.dump(predictions, file)
        with open(self.references_path, "wb") as file:
            pickle.dump(references, file)
        print(f"\nGeneration finished.")
        print(f"Saved {len(predictions)} predictions to {self.predictions_path}")
        print(f"Saved {len(references)} references to {self.references_path}")
        with open(self.predictions_json_path, "w", encoding="utf-8") as f:
            for item in json_results:
                f.write(json.dumps(item, ensure_ascii=False) + "\n")
        print(f"Saved human-readable results to {self.predictions_json_path}")


def main():
    trainer = Trainer()
    if args.mode == "finetune":
        trainer.train()
    elif args.mode == "generate":
        trainer.generate()


if __name__ == "__main__":
    main()