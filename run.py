# run.py (最终版)

import sys
import os

# --- 核心修改：强制将项目根目录添加到搜索路径 ---
# 1. 获取当前run.py文件所在的目录的绝对路径，这就是我们的项目根目录
project_root = os.path.dirname(os.path.abspath(__file__))
# 2. 将这个路径插入到sys.path列表的最前面 (index 0)
#    这确保了Python会优先在这里寻找模块
if project_root not in sys.path:
    sys.path.insert(0, project_root)
# --- 修改结束 ---


# 现在，由于项目根目录已经在搜索路径中，我们可以安全地使用绝对路径导入
from explainer.utils.parse import args


def main():
    # 根据mode参数，决定执行哪个模块的主程序
    if args.mode == "finetune" or args.mode == "generate":
        # 使用绝对路径导入
        from explainer.train import Trainer

        trainer = Trainer()
        if args.mode == "finetune":
            trainer.train()
        else:
            trainer.generate()

    elif args.mode == "evaluate":
        # 使用绝对路径导入
        from evaluation.main import Evaluator

        evaluator = Evaluator(args)
        evaluator.run_evaluation()

    else:
        print(f"Unknown mode: {args.mode}")


if __name__ == "__main__":
    main()