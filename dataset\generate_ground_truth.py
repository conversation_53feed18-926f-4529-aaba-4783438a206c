import os
import json
import requests  # 导入requests库，用于发送HTTP请求
from tqdm import tqdm
import time
import concurrent.futures

# --- 1. 配置与初始化 ---

# !!! 重要：请先在您的系统中设置环境变量 SILICONFLOW_API_KEY !!!
# Windows: set SILICONFLOW_API_KEY=sk-your_key
# Linux/macOS: export SILICONFLOW_API_KEY='sk-your_key'
# 或者，直接取消下面这行的注释，并填入您在硅基流动官网上获取的API Key
os.environ["SILICONFLOW_API_KEY"] = "sk-bfbsczutzvmerrwhtmxmuqpiplpriaxfvlvsixxefbhovegi"

# 硅基流动API的配置
API_URL = "https://api.siliconflow.cn/v1/chat/completions"
API_KEY = os.getenv("SILICONFLOW_API_KEY")
MODEL_NAME = "Qwen/Qwen3-8B"  # 使用通义千问2-7B指导模型，性价比高

if not API_KEY:
    print("API Key未配置，请设置环境变量 SILICONFLOW_API_KEY 或直接在代码中指定。")
    exit()

HEADERS = {
    "Authorization": f"Bearer {API_KEY}",
    "Content-Type": "application/json"
}

# 系统提示词，为LLM设定角色和任务
SYSTEM_PROMPT = (
    "你是一位资深的在线教育推荐顾问。你的任务是根据用户的学习意图和一门课程的简介，"
    "生成一段有说服力的、个性化的推荐解释。"
    "解释必须清晰地将学生的学习意图与课程的教学重点联系起来，并且严格控制在50个词以内。"
)

# --- 新增代码：速率控制参数 ---
RPM_LIMIT = 1000
# 为了留出安全边际，我们不把速率拉满，设置为RPM的95%
REQUESTS_PER_MINUTE = RPM_LIMIT * 0.95
# 计算出每个请求之间的最小时间间隔（秒）
DELAY_BETWEEN_REQUESTS = 60.0 / REQUESTS_PER_MINUTE
# --- 新增代码结束 ---

# --- 2. 数据加载与预处理 (与之前版本完全相同) ---

print("开始加载数据...")
# ... (省略了所有数据加载的代码，与之前完全一样) ...
with open('llm_response_user.json', 'r', encoding='utf-8') as f:
    user_intents = json.load(f)
item_map_raw_to_int = {}
with open('item_map.txt', 'r', encoding='utf-8') as f:
    for line in f:
        raw_id, int_id = line.strip().split()
        item_map_raw_to_int[raw_id] = int(int_id)
item_map_int_to_raw = {v: k for k, v in item_map_raw_to_int.items()}
course_abouts = {}
with open('course_about.json', 'r', encoding='utf-8') as f:
    all_courses = json.load(f)
    for course in all_courses:
        course_abouts[course['id']] = course.get('about', '暂无简介')
training_interactions = []
with open('train.txt', 'r', encoding='utf-8') as f:
    for line in f:
        parts = line.strip().split()
        user_id = int(parts[0])
        for item_id in parts[1:]:
            training_interactions.append({'uid': user_id, 'iid': int(item_id)})
print(f"数据加载完成！共找到 {len(training_interactions)} 条训练交互。")


# --- 3. 定义API调用函数 (全新版本) ---

def generate_explanation_for_interaction(interaction):
    """为单条交互生成解释 (使用requests和指数退避重试)"""
    user_id = interaction['uid']
    item_id = interaction['iid']

    max_retries = 5
    base_delay = 2

    for attempt in range(max_retries):
        try:
            # --- 数据对齐与Prompt构建 (与之前相同) ---
            user_intent_text = user_intents.get(str(user_id), "该用户暂无明确的学习意图。")
            raw_item_id = item_map_int_to_raw.get(item_id)
            if not raw_item_id:
                return None
            course_about_text = course_abouts.get(raw_item_id, "暂无课程简介。")
            user_prompt = (
                f"请基于以下信息生成推荐解释：\n\n"
                f"### 用户的学习意图:\n{user_intent_text}\n\n"
                f"### 课程的简介:\n{course_about_text}\n\n"
                f"### 你的任务:\n请为这位用户推荐这门课程，并生成一段不超过50词的解释。"
            )

            # --- 构建API请求的payload (全新) ---
            payload = {
                "model": MODEL_NAME,
                "messages": [
                    {"role": "system", "content": SYSTEM_PROMPT},
                    {"role": "user", "content": user_prompt}
                ],
                "temperature": 0.7,
                "max_tokens": 150  # 限制生成长度，50个中文词大约需要100-120个token
            }

            # --- 发送POST请求 (全新) ---
            response = requests.post(API_URL, json=payload, headers=HEADERS, timeout=60)  # 设置60秒超时

            # 检查HTTP状态码，如果不成功则抛出异常
            response.raise_for_status()

            # 解析返回的JSON数据
            response_data = response.json()

            # 检查API返回的内部是否有错误
            if "error" in response_data:
                raise Exception(f"API Error: {response_data['error']}")

            explanation = response_data['choices'][0]['message']['content']

            # 如果成功，立即返回结果
            return {
                "uid": user_id,
                "iid": item_id,
                "explanation": explanation.strip()
            }

        except requests.exceptions.RequestException as e:  # 捕获网络层面的错误
            print(f"处理 uid={user_id}, iid={item_id} 时发生网络错误: {e}。这是第 {attempt + 1} 次尝试。")
            if attempt < max_retries - 1:
                delay = base_delay * (2 ** attempt)
                print(f"将在 {delay} 秒后重试...")
                time.sleep(delay)
            else:
                print("已达到最大重试次数，放弃此条交互。")
                return None

        except Exception as e:  # 捕获所有其他错误（包括API内部错误、JSON解析错误等）
            print(f"处理 uid={user_id}, iid={item_id} 时发生未知错误: {e}")
            return None

    return None


# --- 4. 主执行流程 (与之前版本完全相同) ---

output_filename = 'ground_truth_explanations.json'

already_processed = set()
try:
    with open(output_filename, 'r', encoding='utf-8') as f:
        for line in f:
            processed_item = json.loads(line)
            uid = processed_item['uid']
            iid = processed_item['iid']
            already_processed.add((uid, iid))
    print(f"检测到已存在的输出文件，已加载 {len(already_processed)} 条已处理的记录。")
except FileNotFoundError:
    print("未找到已存在的输出文件，将从头开始生成。")

interactions_to_process = [
    inter for inter in training_interactions
    if (inter['uid'], inter['iid']) not in already_processed
]

if not interactions_to_process:
    print("所有交互均已处理完毕，无需生成新解释。程序退出。")
    exit()

print(f"准备开始为 {len(interactions_to_process)} 条新交互生成解释...")

MAX_WORKERS = 20  # 免费API的速率限制通常更严格，建议从一个更小的值开始

with open(output_filename, 'a', encoding='utf-8') as output_file:
    with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        future_to_interaction = {executor.submit(generate_explanation_for_interaction, inter): inter for inter in
                                 interactions_to_process}

        for future in tqdm(concurrent.futures.as_completed(future_to_interaction), total=len(interactions_to_process)):
            try:
                result = future.result()
                if result:
                    output_file.write(json.dumps(result, ensure_ascii=False) + '\n')
            except Exception as exc:
                print(f'生成过程中产生了一个异常: {exc}')

print(f"\n处理完成！本次运行新增了 {len(interactions_to_process)} 条解释。")
print(f"结果已追加保存到文件: {output_filename}")