import os
import json
import requests
from tqdm import tqdm
import time
import concurrent.futures

# --- 1. 配置与初始化 (与生成脚本完全相同) ---
os.environ["SILICONFLOW_API_KEY"] = "sk-bfbsczutzvmerrwhtmxmuqpiplpriaxfvlvsixxefbhovegi"
# !!! 确保环境变量 SILICONFLOW_API_KEY 已正确设置 !!!
API_URL = "https://api.siliconflow.cn/v1/chat/completions"
API_KEY = os.getenv("SILICONFLOW_API_KEY")
MODEL_NAME = "Qwen/Qwen3-8B"

if not API_KEY:
    print("API Key未配置...")
    exit()

HEADERS = {
    "Authorization": f"Bearer {API_KEY}",
    "Content-Type": "application/json"
}
SYSTEM_PROMPT = (
    "你是一位资深的在线教育推荐顾问。你的任务是根据用户的学习意图和一门课程的简介，"
    "生成一段有说服力的、个性化的推荐解释。"
    "解释必须清晰地将学生的学习意图与课程的教学重点联系起来，并且严格控制在50个词以内。"
)

# --- 2. 数据加载 (与生成脚本完全相同) ---

print("开始加载数据...")
# ... (省略了所有数据加载的代码) ...
with open('llm_response_user.json', 'r', encoding='utf-8') as f:
    user_intents = json.load(f)
item_map_raw_to_int = {}
with open('item_map.txt', 'r', encoding='utf-8') as f:
    for line in f:
        raw_id, int_id = line.strip().split()
        item_map_raw_to_int[raw_id] = int(int_id)
item_map_int_to_raw = {v: k for k, v in item_map_raw_to_int.items()}
course_abouts = {}
with open('course_about.json', 'r', encoding='utf-8') as f:
    all_courses = json.load(f)
    for course in all_courses:
        course_abouts[course['id']] = course.get('about', '暂无简介')
print("数据加载完成！")


# --- 3. 定义API调用函数 (与生成脚本完全相同) ---

def generate_explanation_for_interaction(interaction):
    # ... (这个函数内部逻辑完全不变，保留指数退避重试) ...
    user_id = interaction['uid']
    item_id = interaction['iid']
    # ... (省略了完整的函数体，因为它和之前的版本一样) ...
    try:
        user_intent_text = user_intents.get(str(user_id), "该用户暂无明确的学习意图。")
        raw_item_id = item_map_int_to_raw.get(item_id)
        if not raw_item_id: return None
        course_about_text = course_abouts.get(raw_item_id, "暂无课程简介。")
        user_prompt = (
            f"请基于以下信息生成推荐解释：\n\n"
            f"### 用户的学习意图:\n{user_intent_text}\n\n"
            f"### 课程的简介:\n{course_about_text}\n\n"
            f"### 你的任务:\n请为这位用户推荐这门课程，并生成一段不超过50词的解释。"
        )
        payload = {"model": MODEL_NAME,
                   "messages": [{"role": "system", "content": SYSTEM_PROMPT}, {"role": "user", "content": user_prompt}],
                   "temperature": 0.7, "max_tokens": 150}
        response = requests.post(API_URL, json=payload, headers=HEADERS, timeout=60)
        response.raise_for_status()
        response_data = response.json()
        if "error" in response_data: raise Exception(f"API Error: {response_data['error']}")
        explanation = response_data['choices'][0]['message']['content']
        return {"uid": user_id, "iid": item_id, "explanation": explanation.strip()}
    except Exception as e:
        print(f"处理 uid={user_id}, iid={item_id} 时发生错误: {e}")
        return None


# --- 4. 主执行流程 (全新修复逻辑) ---

input_filename = 'test_explanations_full.json'
output_filename = 'test_explanations_full.json'

# --- 新增代码：找出需要修复的样本 ---
all_explanations = []
interactions_to_fix = []

print(f"正在读取并检查文件: {input_filename}")
with open(input_filename, 'r', encoding='utf-8') as f:
    for line in f:
        try:
            data = json.loads(line)
            all_explanations.append(data)
            # 检查explanation字段是否为空或只包含空白字符
            if not data.get("explanation", "").strip():
                interactions_to_fix.append({"uid": data["uid"], "iid": data["iid"]})
        except json.JSONDecodeError:
            print(f"警告：跳过无法解析的行: {line.strip()}")

if not interactions_to_fix:
    print("恭喜！所有解释都非空，无需修复。程序退出。")
    exit()

print(f"检查完成！共找到 {len(interactions_to_fix)} 条空的解释需要重新生成。")
# --- 新增代码结束 ---


# --- 调用API为坏样本重新生成解释 ---
MAX_WORKERS = 5
fixed_explanations = {}  # 使用字典来存储修复后的结果，方便快速查找

with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
    future_to_interaction = {executor.submit(generate_explanation_for_interaction, inter): inter for inter in
                             interactions_to_fix}

    for future in tqdm(concurrent.futures.as_completed(future_to_interaction), total=len(interactions_to_fix),
                       desc="重新生成解释"):
        try:
            result = future.result()
            if result and result.get("explanation", "").strip():
                # 将修复成功的结果存入字典，键为(uid, iid)元组
                fixed_explanations[(result["uid"], result["iid"])] = result["explanation"]
        except Exception as exc:
            print(f'修复过程中产生了一个异常: {exc}')

print(f"修复完成！成功为 {len(fixed_explanations)} 条记录生成了新解释。")

# --- 新增代码：更新原始数据并保存到新文件 ---
updated_count = 0
with open(output_filename, 'w', encoding='utf-8') as f_out:
    for data in tqdm(all_explanations, desc="正在写入修复后的文件"):
        key = (data["uid"], data["iid"])
        # 如果当前记录在我们的修复字典中，说明它之前是空的，现在需要更新
        if key in fixed_explanations:
            # 用新生成的解释替换掉空的解释
            data["explanation"] = fixed_explanations[key]
            updated_count += 1

        # 将（可能已更新的）记录写入新文件
        f_out.write(json.dumps(data, ensure_ascii=False) + '\n')

print(f"\n全部完成！共更新了 {updated_count} 条记录。")
print(f"修复后的完整数据已保存到新文件: {output_filename}")