# evaluation/main.py

import os
import pickle
import json
from explainer.utils.parse import args
from evaluation.metrics import (
    calculate_bert_score,
    calculate_bleurt_score,
    calculate_bart_score,
    calculate_usr,
    GPTScorer
)


class Evaluator:
    def __init__(self, args):
        print(f"Initializing evaluator for dataset: {args.dataset}")

        # --- 1. 定义文件路径 ---
        self.results_dir = f"./results/{args.dataset}/"
        self.pred_path = os.path.join(self.results_dir, "predictions.pkl")
        self.ref_path = os.path.join(self.results_dir, "references.pkl")

        # 定义GPTScore所需的Prompt文件路径
        self.gptscore_prompt_path = "./evaluation/prompts/gptscore_system_prompt.txt"

        # --- 2. 加载数据 ---
        try:
            with open(self.pred_path, "rb") as f:
                self.predictions = pickle.load(f)
            with open(self.ref_path, "rb") as f:
                self.references = pickle.load(f)
            print(f"Successfully loaded {len(self.predictions)} predictions and {len(self.references)} references.")
        except FileNotFoundError:
            print("Error: Prediction or reference files not found.")
            print(f"Looked for: {self.pred_path} and {self.ref_path}")
            exit()

    def run_evaluation(self):
        """执行所有评估并打印结果"""
        all_scores = {}

        # --- 计算本地指标 ---
        # 这些指标不需要API调用，可以快速完成
        bert_scores = calculate_bert_score(self.predictions, self.references)
        all_scores.update(bert_scores)

        bleurt_scores = calculate_bleurt_score(self.predictions, self.references)
        all_scores.update(bleurt_scores)

        # BARTScore可能比较慢，可以根据需要注释掉
        # bart_scores = calculate_bart_score(self.predictions, self.references)
        # all_scores.update(bart_scores)

        usr_score = calculate_usr(self.predictions)
        all_scores.update(usr_score)

        # --- 计算GPTScore (需要API) ---
        # 从环境变量中获取API Key
        api_key = os.getenv("SILICONFLOW_API_KEY")
        if api_key:
            with open(self.gptscore_prompt_path, "r", encoding="utf-8") as f:
                system_prompt = f.read()

            # 实例化GPTScorer
            gpt_scorer = GPTScorer(api_key=api_key, system_prompt=system_prompt)
            # 计算分数
            gpt_scores = gpt_scorer.calculate_gpt_score(self.predictions, self.references)
            all_scores.update(gpt_scores)
        else:
            print("\nWarning: SILICONFLOW_API_KEY not found in environment variables. Skipping GPTScore calculation.")

        # --- 打印最终结果 ---
        self.print_scores(all_scores)

    def print_scores(self, scores):
        """格式化打印所有分数 (健壮版)"""
        print("\n" + "=" * 50)
        print(f"Evaluation Results for Dataset: {args.dataset}")
        print("=" * 50)

        def format_score(metric_name):
            """一个内部辅助函数，用于安全地格式化分数"""
            mean_key = f"{metric_name}_mean"
            std_key = f"{metric_name}_std"
            mean_val = scores.get(mean_key)
            std_val = scores.get(std_key)

            if isinstance(mean_val, float) and isinstance(std_val, float):
                return f"{mean_val:.4f}  (±{std_val:.4f})"
            elif isinstance(mean_val, float):
                return f"{mean_val:.4f}"
            else:
                return "N/A (Calculation Failed)"

        print("\n--- Explanability Evaluation Metrics ---")
        print(f"BERTScore-F1:       {format_score('bert_f1')}")
        print(f"BLEURT:             {format_score('bleurt')}")
        print(f"GPTScore (Intent):  {format_score('gpt_score')}")
        print(f"USR (Diversity):    {scores.get('usr', 'N/A'):.4f}")  # USR总是float，可以保留

        print("\n--- Detailed BERTScore ---")
        print(f"BERTScore-Precision:{format_score('bert_precision')}")
        print(f"BERTScore-Recall:   {format_score('bert_recall')}")

        print("\n" + "=" * 50)


# --- 程序主入口 ---
if __name__ == "__main__":
    # 实例化评估器
    evaluator = Evaluator()
    # 运行评估
    evaluator.run_evaluation()