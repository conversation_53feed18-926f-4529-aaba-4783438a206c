import json
import random
from collections import defaultdict

# --- 1. 配置 ---
# (文件名配置与之前相同)
interaction_filename = 'test.txt'
explanation_filename = 'test_explanations_full.json'
val_interaction_filename = 'val.txt'
test_interaction_filename = 'test.txt'
val_explanation_filename = 'validation_explanations.json'
test_explanation_filename = 'test_explanations.json'

# 设置随机种子
random.seed(42)

# --- 2. 加载数据并整合成统一的样本列表 ---

# 加载解释数据 (以它为主体，因为它是一一对应的)
all_samples = []
try:
    with open(explanation_filename, 'r', encoding='utf-8') as f:
        for line in f:
            # 每一行就是一个独立的样本
            all_samples.append(json.loads(line))
except FileNotFoundError:
    print(f"错误：输入文件 {explanation_filename} 未找到。")
    exit()

print(f"从 {explanation_filename} 加载了 {len(all_samples)} 条独立的交互解释样本。")

# (我们不再需要单独加载 test.txt，因为所有信息都在 all_samples 中了)

# --- 3. 对统一的样本列表进行随机划分 ---

# 随机打乱样本列表
random.shuffle(all_samples)

# 计算分割点
split_point = len(all_samples) // 2

# 将样本列表一分为二
validation_samples = all_samples[:split_point]
test_samples = all_samples[split_point:]

print(f"\n将样本随机划分为 {len(validation_samples)} 条验证集样本和 {len(test_samples)} 条测试集样本。")


# --- 4. 根据划分好的样本集合，生成新的文件 ---

def save_sets_to_files(samples, interaction_filename, explanation_filename):
    """
    一个统一的函数，接收一个样本列表，
    然后分别生成对应的交互txt文件和解释jsonl文件。
    """
    # 用于构建交互txt文件的数据结构
    interactions_by_user = defaultdict(list)

    # 打开解释文件进行写入
    with open(explanation_filename, 'w', encoding='utf-8') as f_exp:
        # 遍历这个集合中的所有样本
        for sample in samples:
            uid = sample['uid']
            iid = sample['iid']

            # 1. 将交互信息存入字典
            interactions_by_user[uid].append(iid)

            # 2. 将解释信息直接写入文件
            f_exp.write(json.dumps(sample, ensure_ascii=False) + '\n')

    # 打开交互文件进行写入
    with open(interaction_filename, 'w', encoding='utf-8') as f_int:
        # 遍历所有用户，按用户ID排序写入，保持文件整洁
        for user_id in sorted(interactions_by_user.keys()):
            items = interactions_by_user[user_id]
            items_str = ' '.join(map(str, items))
            f_int.write(f"{user_id} {items_str}\n")


# --- 保存验证集文件 ---
print("\n正在生成验证集文件...")
save_sets_to_files(validation_samples, val_interaction_filename, val_explanation_filename)
print(f"新的验证集交互历史已保存到: {val_interaction_filename}")
print(f"新的验证集解释已保存到: {val_explanation_filename}")

# --- 保存测试集文件 ---
print("\n正在生成测试集文件...")
save_sets_to_files(test_samples, test_interaction_filename, test_explanation_filename)
print(f"新的测试集交互历史已保存到: {test_interaction_filename}")
print(f"新的测试集解释已保存到: {test_explanation_filename}")

print("\n数据集按样本随机划分完成！所有文件均已对齐。")