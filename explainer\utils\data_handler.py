import torch
import pickle
import json
from torch.utils.data import Dataset, DataLoader
from tqdm import tqdm
from explainer.utils.parse import args
from typing import List
from transformers import AutoTokenizer


# --- 自定义数据集类 (不变) ---
class IDECoTDataset(Dataset):
    def __init__(self, data: List[tuple]):
        self.data = data

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        return self.data[idx]


# --- 数据处理器 (最终修复版) ---
class DataHandler:
    def __init__(self):
        # --- 步骤1: 定义文件路径 (不变) ---
        self.user_intent_vec_path = "./dataset/user_final_embeddings.pt"
        self.item_intent_vec_path = "./dataset/item_final_embeddings.pt"
        self.user_intent_text_path = "./dataset/llm_response_user.json"
        self.item_intent_text_path = "./dataset/llm_response_item.json"
        self.trn_exp_path = "./dataset/train_explanations.json"
        self.val_exp_path = "./dataset/validation_explanations.json"
        self.tst_exp_path = "./dataset/test_explanations.json"

        # --- 步骤2: 加载所有核心数据到内存 (不变) ---
        print("Loading all necessary data assets...")
        model_name = "Qwen/Qwen1.5-1.8B-Chat"
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)

        self.user_intent_vecs = torch.load(self.user_intent_vec_path, map_location='cpu')
        self.item_intent_vecs = torch.load(self.item_intent_vec_path, map_location='cpu')
        with open(self.user_intent_text_path, "r", encoding="utf-8") as f:
            self.user_intent_texts = json.load(f)
        with open(self.item_intent_text_path, "r", encoding="utf-8") as f:
            self.item_intent_texts = json.load(f)
        print("Data loading complete.")

    # --- 核心修改1: 将collate_fn定义为类的静态方法 ---
    @staticmethod
    def collate_fn_train(batch):
        """处理训练批次的静态方法"""
        user_vecs = torch.stack([item[0] for item in batch])
        item_vecs = torch.stack([item[1] for item in batch])
        prompts = [item[2] for item in batch]
        return user_vecs, item_vecs, prompts

    @staticmethod
    def collate_fn_eval(batch):
        """处理评估批次的静态方法"""
        user_vecs = torch.stack([item[0] for item in batch])
        item_vecs = torch.stack([item[1] for item in batch])
        prompts = [item[2] for item in batch]
        explanations = [item[3] for item in batch]
        return user_vecs, item_vecs, prompts, explanations

    # --- 修改结束 ---

    def _load_and_prepare_data(self, explanation_path, is_training=True):
        """一个内部辅助函数，用于加载解释文件并构建最终的输入列表"""
        explanation_data = []
        with open(explanation_path, "r", encoding="utf-8") as f:
            for line in f:
                explanation_data.append(json.loads(line))

        if args.debug:
            print(f"--- DEBUG MODE ON: Truncating data to 16 samples for {explanation_path} ---")
            explanation_data = explanation_data[:16]

        processed_input = []
        system_prompt = "用一句话清晰地解释为什么这门特定的课程适合这位特定的用户，解释的长度严格禁止超过50个汉字"

        for sample in tqdm(explanation_data, desc=f"Preparing {'training' if is_training else 'evaluation'} data"):
            uid = sample["uid"]
            iid = sample["iid"]
            user_vec = self.user_intent_vecs[uid]
            item_vec = self.item_intent_vecs[iid]
            user_text = self.user_intent_texts.get(str(uid), "用户意图未知。")
            item_text = self.item_intent_texts.get(str(iid), "课程意图未知。")

            user_content = (
                f"用户的学习意图: {user_text}\n"
                f"课程的教学意图: {item_text}\n"
                f"协同信号: <USER_INTENT_VEC> <ITEM_INTENT_VEC>\n"
                f"<EXPLAIN_POS>"
            )

            if is_training:
                messages = [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_content},
                    {"role": "assistant", "content": sample["explanation"]}
                ]
                full_prompt_text = self.tokenizer.apply_chat_template(
                    messages, tokenize=False, add_generation_prompt=False
                )
                processed_input.append((user_vec, item_vec, full_prompt_text))
            else:
                messages = [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_content}
                ]
                prompt_until_pos = self.tokenizer.apply_chat_template(
                    messages, tokenize=False, add_generation_prompt=True
                )
                processed_input.append((user_vec, item_vec, prompt_until_pos, sample["explanation"]))

        return processed_input

    def get_data_loaders(self):
        """主函数，返回所有DataLoader"""
        trn_input = self._load_and_prepare_data(self.trn_exp_path, is_training=True)
        trn_dataset = IDECoTDataset(trn_input)

        # --- 核心修改2: 调用静态方法 ---
        trn_loader = DataLoader(
            trn_dataset,
            batch_size=args.batch_size,
            shuffle=True,
            collate_fn=DataHandler.collate_fn_train,  # <--- 修改
            num_workers=4 if args.debug is False else 0  # 调试模式下用单进程，避免额外开销
        )

        val_input = self._load_and_prepare_data(self.val_exp_path, is_training=False)
        val_dataset = IDECoTDataset(val_input)
        val_loader = DataLoader(
            val_dataset,
            batch_size=args.batch_size,
            shuffle=False,
            collate_fn=DataHandler.collate_fn_eval,  # <--- 修改
            num_workers=4 if args.debug is False else 0
        )

        tst_input = self._load_and_prepare_data(self.tst_exp_path, is_training=False)
        tst_dataset = IDECoTDataset(tst_input)
        tst_loader = DataLoader(
            tst_dataset,
            batch_size=args.batch_size,
            shuffle=False,
            collate_fn=DataHandler.collate_fn_eval,  # <--- 修改
            num_workers=4 if args.debug is False else 0
        )

        return trn_loader, val_loader, tst_loader