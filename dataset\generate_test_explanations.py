import os
import json
import requests
from tqdm import tqdm
import time
import concurrent.futures

os.environ["SILICONFLOW_API_KEY"] = "sk-bfbsczutzvmerrwhtmxmuqpiplpriaxfvlvsixxefbhovegi"
# --- 1. 配置与初始化 (与之前版本完全相同) ---
# ... (省略了API Key, Headers, System Prompt 等配置) ...
API_URL = "https://api.siliconflow.cn/v1/chat/completions"
API_KEY = os.getenv("SILICONFLOW_API_KEY")
MODEL_NAME = "Qwen/Qwen3-8B"
if not API_KEY:
    print("API Key未配置...")
    exit()
HEADERS = { "Authorization": f"Bearer {API_KEY}", "Content-Type": "application/json" }
SYSTEM_PROMPT = (
    "你是一位资深的在线教育推荐顾问。你的任务是根据用户的学习意图和一门课程的简介，"
    "生成一段有说服力的、个性化的推荐解释。"
    "解释必须清晰地将学生的学习意图与课程的教学重点联系起来，并且严格控制在50个词以内。"
)
RPM_LIMIT = 1000
REQUESTS_PER_MINUTE = RPM_LIMIT * 0.95
DELAY_BETWEEN_REQUESTS = 60.0 / REQUESTS_PER_MINUTE

# --- 2. 数据加载与预处理 (与之前版本完全相同) ---
print("开始加载数据...")
# ... (省略了 user_intents, item_map, course_abouts 的加载) ...
with open('llm_response_user.json', 'r', encoding='utf-8') as f:
    user_intents = json.load(f)
item_map_raw_to_int = {}
with open('item_map.txt', 'r', encoding='utf-8') as f:
    for line in f:
        raw_id, int_id = line.strip().split()
        item_map_raw_to_int[raw_id] = int(int_id)
item_map_int_to_raw = {v: k for k, v in item_map_raw_to_int.items()}
course_abouts = {}
with open('course_about.json', 'r', encoding='utf-8') as f:
    all_courses = json.load(f)
    for course in all_courses:
        course_abouts[course['id']] = course.get('about', '暂无简介')

# --- 修改代码：加载 test.txt ---
test_interactions = []
with open('test.txt', 'r', encoding='utf-8') as f:
    for line in f:
        parts = line.strip().split()
        user_id = int(parts[0])
        for item_id in parts[1:]:
            test_interactions.append({'uid': user_id, 'iid': int(item_id)})
print(f"数据加载完成！共找到 {len(test_interactions)} 条测试交互。")
# --- 修改代码结束 ---


# --- 3. 定义API调用函数 (与之前版本完全相同) ---
def generate_explanation_for_interaction(interaction):
    # ... (这个函数内部逻辑完全不变，保留指数退避重试) ...
    # ... (省略了完整的函数体) ...
    user_id = interaction['uid']
    item_id = interaction['iid']
    max_retries = 5
    base_delay = 2
    for attempt in range(max_retries):
        try:
            user_intent_text = user_intents.get(str(user_id), "该用户暂无明确的学习意图。")
            raw_item_id = item_map_int_to_raw.get(item_id)
            if not raw_item_id: return None
            course_about_text = course_abouts.get(raw_item_id, "暂无课程简介。")
            user_prompt = (
                f"请基于以下信息生成推荐解释：\n\n"
                f"### 用户的学习意图:\n{user_intent_text}\n\n"
                f"### 课程的简介:\n{course_about_text}\n\n"
                f"### 你的任务:\n请为这位用户推荐这门课程，并生成一段不超过50词的解释。"
            )
            payload = { "model": MODEL_NAME, "messages": [{"role": "system", "content": SYSTEM_PROMPT}, {"role": "user", "content": user_prompt}], "temperature": 0.7, "max_tokens": 150 }
            response = requests.post(API_URL, json=payload, headers=HEADERS, timeout=60)
            response.raise_for_status()
            response_data = response.json()
            if "error" in response_data: raise Exception(f"API Error: {response_data['error']}")
            explanation = response_data['choices'][0]['message']['content']
            return { "uid": user_id, "iid": item_id, "explanation": explanation.strip() }
        except requests.exceptions.RequestException as e:
            if attempt < max_retries - 1: time.sleep(base_delay * (2 ** attempt))
        except Exception as e:
            return None
    return None

# --- 4. 主执行流程 (与v3版本完全相同) ---

output_filename = 'test_explanations_full.json'

already_processed = set()
try:
    with open(output_filename, 'r', encoding='utf-8') as f:
        for line in f:
            processed_item = json.loads(line)
            already_processed.add((processed_item['uid'], processed_item['iid']))
    print(f"检测到已存在的输出文件，已加载 {len(already_processed)} 条已处理的记录。")
except FileNotFoundError:
    print("未找到已存在的输出文件，将从头开始生成。")

interactions_to_process = [
    inter for inter in test_interactions
    if (inter['uid'], inter['iid']) not in already_processed
]

if not interactions_to_process:
    print("所有测试集交互均已处理完毕。程序退出。")
    exit()

print(f"准备开始为 {len(interactions_to_process)} 条新的测试交互生成解释...")

MAX_WORKERS = 20

with open(output_filename, 'a', encoding='utf-8') as output_file:
    with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        pbar = tqdm(total=len(interactions_to_process))
        future_to_interaction = {executor.submit(generate_explanation_for_interaction, inter): inter for inter in interactions_to_process}
        for future in concurrent.futures.as_completed(future_to_interaction):
            try:
                result = future.result()
                if result:
                    output_file.write(json.dumps(result, ensure_ascii=False) + '\n')
                    output_file.flush()
            except Exception as exc:
                print(f'一个任务在主线程中产生异常: {exc}')
            time.sleep(DELAY_BETWEEN_REQUESTS)
            pbar.update(1)
        pbar.close()

print(f"\n处理完成！测试集解释已全部生成并保存到: {output_filename}")