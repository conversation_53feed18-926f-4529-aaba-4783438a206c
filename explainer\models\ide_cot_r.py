# explainer/models/ide_cot_r.py

import torch
import torch.nn as nn
import torch.nn.functional as F
# 导入Hugging Face官方标准的模型和分词器
from transformers import AutoTokenizer, AutoModelForCausalLM


# -------------------------------------------------------------------
# 1. MoE适配器层 (这部分代码可以直接复用，只需在实例化时调整维度)
# -------------------------------------------------------------------
class PWLayer(nn.Module):
    """Single Parametric Whitening Layer"""

    def __init__(self, input_size, output_size, dropout=0.0):
        super(PWLayer, self).__init__()
        self.dropout = nn.Dropout(p=dropout)
        self.bias = nn.Parameter(torch.zeros(input_size), requires_grad=True)
        self.lin = nn.Linear(input_size, output_size, bias=False)
        self.apply(self._init_weights)

    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            module.weight.data.normal_(mean=0.0, std=0.02)

    def forward(self, x):
        return self.lin(self.dropout(x) - self.bias)


class MoEAdaptorLayer(nn.Module):
    """MoE-enhanced Adaptor"""

    def __init__(self, n_exps=8, layers=[64, 2048], dropout=0.2, noise=True):
        super(MoEAdaptorLayer, self).__init__()
        self.n_exps = n_exps
        self.noisy_gating = noise
        self.experts = nn.ModuleList([PWLayer(layers[0], layers[1], dropout) for i in range(n_exps)])
        self.w_gate = nn.Parameter(torch.zeros(layers[0], n_exps), requires_grad=True)
        self.w_noise = nn.Parameter(torch.zeros(layers[0], n_exps), requires_grad=True)

    def noisy_top_k_gating(self, x, train, noise_epsilon=1e-2):
        clean_logits = x @ self.w_gate
        if self.noisy_gating and train:
            raw_noise_stddev = x @ self.w_noise
            noise_stddev = F.softplus(raw_noise_stddev) + noise_epsilon
            noisy_logits = clean_logits + (torch.randn_like(clean_logits).to(x.device) * noise_stddev)
            logits = noisy_logits
        else:
            logits = clean_logits
        gates = F.softmax(logits, dim=-1)
        return gates

    def forward(self, x):
        gates = self.noisy_top_k_gating(x, self.training)
        expert_outputs = [self.experts[i](x).unsqueeze(-2) for i in range(self.n_exps)]
        expert_outputs = torch.cat(expert_outputs, dim=-2)
        multiple_outputs = gates.unsqueeze(-1) * expert_outputs
        return multiple_outputs.sum(dim=-2)


# -------------------------------------------------------------------
# 2. 您的Explainer主模型 (修改版)
# -------------------------------------------------------------------
class Explainer(nn.Module):
    def __init__(self, mdir_vector_size=512):  
        super(Explainer, self).__init__()

        # --- 修改1: 加载Qwen-1.5B模型 ---
        print("Loading Qwen-1.5-1.8B-Chat model...")
        model_name = "Qwen/Qwen1.5-1.8B-Chat"
        # 使用torch_dtype="auto"让transformers自动选择最佳精度（在4070上通常是bfloat16）
        # 移除了 load_in_8bit=True，因为它依赖bitsandbytes
        self.model = AutoModelForCausalLM.from_pretrained(model_name, torch_dtype="auto")
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)

        # 自动获取Qwen模型的隐藏层维度
        qwen_hidden_size = self.model.config.hidden_size
        print(f"Qwen hidden size: {qwen_hidden_size}")

        # --- 修改2: 扩展词汇表和修改特殊令牌 ---
        # 使用我们新设计的、更具语义的特殊令牌
        special_tokens_dict = {
            "additional_special_tokens": ["<USER_INTENT_VEC>", "<ITEM_INTENT_VEC>", "<EXPLAIN_POS>"],
            "pad_token": "[PAD]"  # <--- 新增一个专门的填充Token
        }
        self.tokenizer.add_special_tokens(special_tokens_dict)
        # Qwen模型通常使用eos_token作为pad_token
        # self.tokenizer.pad_token = self.tokenizer.eos_token
        self.model.resize_token_embeddings(len(self.tokenizer))

        # --- 冻结LLM参数 (逻辑不变) ---
        print("Freezing base LLM parameters...")
        for param in self.model.parameters():
            param.requires_grad = False

        # --- 实例化MoE适配器 (调整维度) ---
        print("Initializing MoE adaptors...")
        self.user_adapter = MoEAdaptorLayer(n_exps=8, layers=[mdir_vector_size, qwen_hidden_size])
        self.item_adapter = MoEAdaptorLayer(n_exps=8, layers=[mdir_vector_size, qwen_hidden_size])

    def forward(self, user_intent_vec, item_intent_vec, full_prompt_text):
        adapted_user_embed = self.user_adapter(user_intent_vec)
        adapted_item_embed = self.item_adapter(item_intent_vec)

        tokenized_inputs = self.tokenizer(
            full_prompt_text, padding=True, return_tensors="pt"
        ).to(self.model.device)
        input_ids = tokenized_inputs.input_ids
        inputs_embeds = self.model.get_input_embeddings()(input_ids)

        adapted_user_embed = adapted_user_embed.to(inputs_embeds.dtype)
        adapted_item_embed = adapted_item_embed.to(inputs_embeds.dtype)

        user_vec_token_id = self.tokenizer.convert_tokens_to_ids("<USER_INTENT_VEC>")
        item_vec_token_id = self.tokenizer.convert_tokens_to_ids("<ITEM_INTENT_VEC>")

        # --- 鲁棒的向量化实现 ---
        batch_indices = torch.arange(input_ids.shape[0], device=self.model.device)

        # 处理用户向量
        user_mask = (input_ids == user_vec_token_id)
        # 检查哪些行（样本）中实际存在用户占位符
        user_rows_with_token = user_mask.any(dim=1)
        if user_rows_with_token.any():
            user_positions = user_mask.int().argmax(dim=1)
            # 只对那些实际存在占位符的行进行赋值
            inputs_embeds[batch_indices[user_rows_with_token], user_positions[user_rows_with_token], :] = \
            adapted_user_embed[user_rows_with_token]

        # 处理物品向量
        item_mask = (input_ids == item_vec_token_id)
        item_rows_with_token = item_mask.any(dim=1)
        if item_rows_with_token.any():
            item_positions = item_mask.int().argmax(dim=1)
            inputs_embeds[batch_indices[item_rows_with_token], item_positions[item_rows_with_token], :] = \
            adapted_item_embed[item_rows_with_token]

        outputs = self.model(
            inputs_embeds=inputs_embeds,
            attention_mask=tokenized_inputs.attention_mask,
            labels=input_ids
        )

        return outputs, input_ids

    def generate(self, user_intent_vec, item_intent_vec, prompt_text_until_pos):
        """用于推理阶段的文本生成 (优化版)"""
        # 1. 适配器翻译 (不变)
        adapted_user_embed = self.user_adapter(user_intent_vec)
        adapted_item_embed = self.item_adapter(item_intent_vec)

        # 2. 文本分词 (不变)
        tokenized_inputs = self.tokenizer(
            prompt_text_until_pos,
            padding=True,
            return_tensors="pt"
        ).to(self.model.device)
        input_ids = tokenized_inputs.input_ids
        # 3. 初始注入 (与优化后的forward方法完全相同的向量化逻辑)
        inputs_embeds = self.model.get_input_embeddings()(input_ids)

        adapted_user_embed = adapted_user_embed.to(inputs_embeds.dtype)
        adapted_item_embed = adapted_item_embed.to(inputs_embeds.dtype)

        user_vec_token_id = self.tokenizer.convert_tokens_to_ids("<USER_INTENT_VEC>")
        item_vec_token_id = self.tokenizer.convert_tokens_to_ids("<ITEM_INTENT_VEC>")

        # --- 鲁棒的向量化实现 ---
        batch_indices = torch.arange(input_ids.shape[0], device=self.model.device)

        # 处理用户向量
        user_mask = (input_ids == user_vec_token_id)
        user_rows_with_token = user_mask.any(dim=1)
        if user_rows_with_token.any():
            user_positions = user_mask.int().argmax(dim=1)
            inputs_embeds[batch_indices[user_rows_with_token], user_positions[user_rows_with_token], :] = adapted_user_embed[user_rows_with_token]

        # 处理物品向量
        item_mask = (input_ids == item_vec_token_id)
        item_rows_with_token = item_mask.any(dim=1)
        if item_rows_with_token.any():
            item_positions = item_mask.int().argmax(dim=1)
            inputs_embeds[batch_indices[item_rows_with_token], item_positions[item_rows_with_token], :] = adapted_item_embed[item_rows_with_token]

        # 4. 调用标准的generate方法 (不变)
        outputs = self.model.generate(
            inputs_embeds=inputs_embeds,
            attention_mask=tokenized_inputs.attention_mask,
            max_new_tokens=128,
            pad_token_id=self.tokenizer.pad_token_id
        )

        # 将生成的token ID序列解码成文本
        output_text = self.tokenizer.batch_decode(outputs, skip_special_tokens=True)
        return output_text