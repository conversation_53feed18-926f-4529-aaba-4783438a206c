<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="53">
            <item index="0" class="java.lang.String" itemvalue="pandas" />
            <item index="1" class="java.lang.String" itemvalue="tqdm" />
            <item index="2" class="java.lang.String" itemvalue="scipy" />
            <item index="3" class="java.lang.String" itemvalue="scikit-learn" />
            <item index="4" class="java.lang.String" itemvalue="torch" />
            <item index="5" class="java.lang.String" itemvalue="numpy" />
            <item index="6" class="java.lang.String" itemvalue="gensim" />
            <item index="7" class="java.lang.String" itemvalue="networkx" />
            <item index="8" class="java.lang.String" itemvalue="node2vec" />
            <item index="9" class="java.lang.String" itemvalue="matplotlib" />
            <item index="10" class="java.lang.String" itemvalue="sklearn" />
            <item index="11" class="java.lang.String" itemvalue="protobuf" />
            <item index="12" class="java.lang.String" itemvalue="torch-scatter" />
            <item index="13" class="java.lang.String" itemvalue="async-timeout" />
            <item index="14" class="java.lang.String" itemvalue="joblib" />
            <item index="15" class="java.lang.String" itemvalue="threadpoolctl" />
            <item index="16" class="java.lang.String" itemvalue="huggingface-hub" />
            <item index="17" class="java.lang.String" itemvalue="PyYAML" />
            <item index="18" class="java.lang.String" itemvalue="python-dateutil" />
            <item index="19" class="java.lang.String" itemvalue="wcwidth" />
            <item index="20" class="java.lang.String" itemvalue="requests" />
            <item index="21" class="java.lang.String" itemvalue="torchvision" />
            <item index="22" class="java.lang.String" itemvalue="frozenlist" />
            <item index="23" class="java.lang.String" itemvalue="filelock" />
            <item index="24" class="java.lang.String" itemvalue="safetensors" />
            <item index="25" class="java.lang.String" itemvalue="certifi" />
            <item index="26" class="java.lang.String" itemvalue="urllib3" />
            <item index="27" class="java.lang.String" itemvalue="pyparsing" />
            <item index="28" class="java.lang.String" itemvalue="torch-geometric" />
            <item index="29" class="java.lang.String" itemvalue="torchaudio" />
            <item index="30" class="java.lang.String" itemvalue="tokenizers" />
            <item index="31" class="java.lang.String" itemvalue="python" />
            <item index="32" class="java.lang.String" itemvalue="transformers" />
            <item index="33" class="java.lang.String" itemvalue="six" />
            <item index="34" class="java.lang.String" itemvalue="tzdata" />
            <item index="35" class="java.lang.String" itemvalue="aiohappyeyeballs" />
            <item index="36" class="java.lang.String" itemvalue="sentence-transformers" />
            <item index="37" class="java.lang.String" itemvalue="attrs" />
            <item index="38" class="java.lang.String" itemvalue="psutil" />
            <item index="39" class="java.lang.String" itemvalue="prettytable" />
            <item index="40" class="java.lang.String" itemvalue="regex" />
            <item index="41" class="java.lang.String" itemvalue="typing_extensions" />
            <item index="42" class="java.lang.String" itemvalue="charset-normalizer" />
            <item index="43" class="java.lang.String" itemvalue="pillow" />
            <item index="44" class="java.lang.String" itemvalue="tensorboardX" />
            <item index="45" class="java.lang.String" itemvalue="aiohttp" />
            <item index="46" class="java.lang.String" itemvalue="multidict" />
            <item index="47" class="java.lang.String" itemvalue="yarl" />
            <item index="48" class="java.lang.String" itemvalue="pytz" />
            <item index="49" class="java.lang.String" itemvalue="aiosignal" />
            <item index="50" class="java.lang.String" itemvalue="setproctitle" />
            <item index="51" class="java.lang.String" itemvalue="idna" />
            <item index="52" class="java.lang.String" itemvalue="fsspec" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyShadowingBuiltinsInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredNames">
        <list>
          <option value="input" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>