# explainer/utils/parse.py

import argparse


def parse_args():
    """
    解析命令行参数。
    所有项目的超参数和配置都在这里定义。
    """
    parser = argparse.ArgumentParser(
        description="IDE-CoT-R: Intent-Driven Explanation via Chain-of-Thought with Reward-Tuning")

    # --- 数据与模型路径参数 ---
    parser.add_argument(
        "--dataset",
        type=str,
        default="mooc-course",  # 将默认值改为您的数据集名称
        help="Name of the dataset directory (e.g., mooc-course, amazon, yelp, google)"
    )

    # --- 训练模式与流程控制参数 ---
    parser.add_argument(
        "--mode",
        type=str,
        default="finetune",
        choices=["finetune", "generate", "evaluate"],  # 使用choices限制输入，防止拼写错误
        help="Set the running mode: 'finetune' for training, 'generate' for generating explanations"
    )
    parser.add_argument(
        "--epochs",
        type=int,
        default=1,  # 建议默认训练多个epoch
        help="Number of training epochs"
    )

    # --- 核心超参数 ---
    parser.add_argument(
        "--batch_size",
        type=int,
        default=1,  # 对于LLM微调，一个较小的batch size是常见的
        help="Batch size for training and generation"
    )
    parser.add_argument(
        "--lr",
        type=float,
        default=1e-4,  # 1e-4 或 2e-5 是PEFT微调中常用的学习率
        help="Learning rate for the Adam optimizer"
    )
    parser.add_argument(
        "--weight_decay",
        type=float,
        default=1e-6,
        help="Weight decay for regularization"
    )

    # --- MoE适配器相关参数 (可选，增加灵活性) ---
    parser.add_argument(
        "--n_exps",
        type=int,
        default=8,
        help="Number of experts in the MoE adapter"
    )
    parser.add_argument(
        "--moe_dropout",
        type=float,
        default=0.2,
        help="Dropout rate within the MoE adapter"
    )

    parser.add_argument(
        "--debug",
        action="store_true",  # 这会将它变成一个开关，命令行里只要有--debug，值就是True
        help="Enable debug mode to run on a small subset of data"
    )



    return parser.parse_args()


# --- 创建一个全局可访问的args对象 ---
# 当其他文件 `from utils.parse import args` 时，它们导入的就是这个已经解析好的对象。
args = parse_args()
